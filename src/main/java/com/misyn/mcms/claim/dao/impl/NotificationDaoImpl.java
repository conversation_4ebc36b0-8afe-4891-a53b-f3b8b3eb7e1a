package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.NotificationDao;
import com.misyn.mcms.claim.dto.DocumentNotificationDto;
import com.misyn.mcms.claim.dto.NotificationDto;
import com.misyn.mcms.claim.dto.UserDto;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class NotificationDaoImpl implements NotificationDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationDaoImpl.class);

    @Override
    public NotificationDto insertMaster(Connection connection, NotificationDto notificationDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_CLAIM_NOTIFICATION);
            ps.setInt(++index, notificationDto.getClaimNo());
            ps.setString(++index, notificationDto.getInpUserId());
            ps.setString(++index, notificationDto.getMessage());
            ps.setString(++index, notificationDto.getReadStatus());
            ps.setString(++index, notificationDto.getReadDateTime());
            ps.setString(++index, notificationDto.getPriorityStatus());
            ps.setString(++index, notificationDto.getNotifyDateTime());
            ps.setString(++index, notificationDto.getIpAddress());
            ps.setString(++index, notificationDto.getAssignUserId());
            ps.setString(++index, notificationDto.getUrl());
            ps.setInt(++index, notificationDto.getRefNo());
            ps.setString(++index, notificationDto.getVehicleNo());
            ps.setString(++index, notificationDto.getCoverNoteNo());
            ps.setString(++index, notificationDto.getAccidentDate());
            ps.setString(++index, notificationDto.getCheckedStatus());
            ps.setString(++index, AppConstant.NO);
            ps.setString(++index, notificationDto.getColorCode());
            ps.setString(++index, notificationDto.getPolicyChannelType());
            ps.setString(++index, notificationDto.getProductName());
            ps.setString(++index, notificationDto.getCategoryDesc());
            if (ps.executeUpdate() > 0) {
                return notificationDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public NotificationDto updateMaster(Connection connection, NotificationDto notificationDto) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_NOTIFICATION);
            ps.setString(++index, notificationDto.getReadStatus());
            ps.setString(++index, notificationDto.getReadDateTime());
            ps.setInt(++index, notificationDto.getTxnId());
            if (ps.executeUpdate() > 0) {
                return notificationDto;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return null;
    }

    @Override
    public boolean updateNotificationChecked(Connection connection, Integer txnId, String status) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(UPDATE_CLAIM_NOTIFICATION_CHECKED);
            ps.setString(++index, status);
            ps.setInt(++index, txnId);
            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return false;
    }

    @Override
    public int insertMasterToHistory(Connection connection, Integer txnId, String status) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_MASTER_TO_HISTORY);
            ps.setString(++index, status);
            ps.setInt(++index, txnId);
            return ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return 0;
    }

    @Override
    public boolean deleteNotification(Connection connection, Integer txnId) throws Exception {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(DELETE_CLAIM_NOTIFICATION);
            ps.setInt(++index, txnId);
            if (ps.executeUpdate() > 0) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
        return false;
    }


    @Override
    public int countByAssignUserId(Connection connection, String assignUserId) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        try {
            ps = connection.prepareStatement(SELECT_COUNT_CLAIM_NOTIFICATION_BY_ASSIGN_USER_ID);
            ps.setString(1, assignUserId);
            rs = ps.executeQuery();
            if (rs.next()) {
                count = rs.getInt("CNT");
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return count;
    }

    @Override
    public int countByAssignUserIdAndReadStatus(Connection connection, String assignUserId, String readStatus) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        try {
            ps = connection.prepareStatement(SELECT_COUNT_CLAIM_NOTIFICATION_BY_ASSIGN_USER_ID_AND_READ_STATUS);
            ps.setString(1, assignUserId);
            ps.setString(2, readStatus);
            rs = ps.executeQuery();
            if (rs.next()) {
                count = rs.getInt("CNT");
            }
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return count;
    }

    @Override
    public List<NotificationDto> searchAllByAssignUserIdWithLimit(Connection connection, String assignUserId, Integer limit) {
        List<NotificationDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_CLAIM_NOTIFICATION_BY_ASSIGN_USER_ID_LIMIT);
            ps.setString(1, assignUserId);
            ps.setInt(2, limit);
            rs = ps.executeQuery();
            while (rs.next()) {
                NotificationDto notificationDto = this.getNotificationDto(rs);
                list.add(notificationDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<NotificationDto> searchMasterAllByAssignUserId(Connection connection, String assignUserId, String fromDate, String toDate, String vehicleNo, String claimNo) {
        int index = 0;
        List<NotificationDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        String SQL = "SELECT * FROM claim_notification WHERE V_ASSIGN_USER_ID = ? ";
        String SQL_ORDER = " ORDER BY V_CHECKED_STATUS DESC,D_NOTIFY_DATE_TIME DESC ";

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL = "SELECT * FROM claim_notification WHERE V_ASSIGN_USER_ID = ? " +
                    "AND CAST(D_NOTIFY_DATE_TIME AS DATE) BETWEEN ? AND ?  ";

            if (!vehicleNo.isEmpty()) {
                SQL = SQL.concat(" AND V_VEHICLE_NO LIKE ? ");
            }
            if (!claimNo.isEmpty()) {
                SQL = SQL.concat(" AND N_CLAIM_NO LIKE ? ");
            }
        } else {
            if (!vehicleNo.isEmpty()) {
                SQL = SQL.concat(" AND V_VEHICLE_NO LIKE ? ");
            }
            if (!claimNo.isEmpty()) {
                SQL = SQL.concat(" AND N_CLAIM_NO LIKE ? ");
            }
        }


        try {
            ps = connection.prepareStatement(SQL.concat(SQL_ORDER));
            if (!fromDate.isEmpty() && !toDate.isEmpty()) {
                ps.setString(++index, assignUserId);
                ps.setString(++index, fromDate);
                ps.setString(++index, toDate);
                if (!vehicleNo.isEmpty()) {
                    ps.setString(++index, "%" + vehicleNo + "%");
                }
                if (!claimNo.isEmpty()) {
                    ps.setString(++index, "%" + claimNo + "%");
                }
            } else {
                ps.setString(++index, assignUserId);
                if (!vehicleNo.isEmpty()) {
                    ps.setString(++index, "%" + vehicleNo + "%");
                }
                if (!claimNo.isEmpty()) {
                    ps.setString(++index, "%" + claimNo + "%");
                }
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                NotificationDto notificationDto = this.getNotificationDto(rs);
                list.add(notificationDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<NotificationDto> searchHistoryAllByAssignUserId(Connection connection, String assignUserId, String fromDate, String toDate, String vehicleNo, String claimNo) {
        int index = 0;
        List<NotificationDto> list = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        String SQL = "SELECT * FROM claim_notification_history WHERE V_ASSIGN_USER_ID = ? ";
        String SQL_ORDER = " ORDER BY V_CHECKED_STATUS DESC,D_NOTIFY_DATE_TIME DESC ";

        if (!fromDate.isEmpty() && !toDate.isEmpty()) {
            SQL = "SELECT * FROM claim_notification_history WHERE V_ASSIGN_USER_ID = ? " +
                    "AND CAST(D_NOTIFY_DATE_TIME AS DATE) BETWEEN ? AND ?  ";

            if (!vehicleNo.isEmpty()) {
                SQL = SQL.concat(" AND V_VEHICLE_NO LIKE ? ");
            }
            if (!claimNo.isEmpty()) {
                SQL = SQL.concat(" AND N_CLAIM_NO LIKE ? ");
            }
        } else {
            if (!vehicleNo.isEmpty()) {
                SQL = SQL.concat(" AND V_VEHICLE_NO LIKE ? ");
            }
            if (!claimNo.isEmpty()) {
                SQL = SQL.concat(" AND N_CLAIM_NO LIKE ? ");
            }
        }


        try {
            ps = connection.prepareStatement(SQL.concat(SQL_ORDER));
            if (!fromDate.isEmpty() && !toDate.isEmpty()) {
                ps.setString(++index, assignUserId);
                ps.setString(++index, fromDate);
                ps.setString(++index, toDate);
                if (!vehicleNo.isEmpty()) {
                    ps.setString(++index, "%" + vehicleNo + "%");
                }
                if (!claimNo.isEmpty()) {
                    ps.setString(++index, "%" + claimNo + "%");
                }
            } else {
                ps.setString(++index, assignUserId);
                if (!vehicleNo.isEmpty()) {
                    ps.setString(++index, "%" + vehicleNo + "%");
                }
                if (!claimNo.isEmpty()) {
                    ps.setString(++index, "%" + claimNo + "%");
                }
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                NotificationDto notificationDto = this.getNotificationDto(rs);
                list.add(notificationDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return list;
    }

    @Override
    public List<DocumentNotificationDto> getPendingNotification(Connection connection) {
        List<DocumentNotificationDto> notificationDtos = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_PENDING_NOTIFICATIONS);
            rs = ps.executeQuery();
            while (rs.next()) {
                DocumentNotificationDto documentNotificationDto = this.getDocumentNotificationDto(rs);
                notificationDtos.add(documentNotificationDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return notificationDtos;
    }

    @Override
    public List<DocumentNotificationDto> getPendingNotificationGroupedByClaimNo(Connection connection) {
        List<DocumentNotificationDto> notificationDtos = new ArrayList<>();
        PreparedStatement ps;
        ResultSet rs;
        try {
            ps = connection.prepareStatement(SELECT_PENDING_NOTIFICATIONS_GROUP_BY_CLAIM_NO);
            rs = ps.executeQuery();
            while (rs.next()) {
                DocumentNotificationDto documentNotificationDto = this.getDocumentNotificationDtoGrouped(rs);
                notificationDtos.add(documentNotificationDto);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return notificationDtos;
    }

    @Override
    public void savePendingNotification(Connection connection, DocumentNotificationDto documentNotificationDto) throws SQLException {
        PreparedStatement ps = null;
        int index = 0;
        try {
            ps = connection.prepareStatement(SAVE_PENDING_NOTIFICATION);
            ps.setLong(++index, documentNotificationDto.getFormRelId());
            ps.setInt(++index, documentNotificationDto.getClaimNo());
            ps.setInt(++index, documentNotificationDto.getDocType());
            ps.setString(++index, documentNotificationDto.getUpdateDate());
            ps.setString(++index, documentNotificationDto.getInputUser());
            ps.setString(++index, documentNotificationDto.getAssignUser());
            ps.setString(++index, documentNotificationDto.getStatus());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public void markSentNotifications(Connection connection, Long formId, String assignUser) throws SQLException {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(MARK_SENT_NOTIFICATIONS);
            ps.setString(1, "Y");
            ps.setLong(2, formId);
            ps.setString(3, assignUser);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public void shiftNotificationOnAction(Connection connection, Integer claimNo, UserDto user) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SHIFT_NOTIFICATION_ON_ACTION);
            ps.setInt(1, claimNo);
            ps.setString(2, user.getUserId());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void deleteShiftedNotification(Connection connection, Integer claimNo, UserDto user) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_SHIFTED_NOTIFICATION);
            ps.setInt(1, claimNo);
            ps.setString(2, user.getUserId());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    private NotificationDto getNotificationDto(ResultSet rs) {
        NotificationDto notificationDto = new NotificationDto();
        try {
            notificationDto.setTxnId(rs.getInt("N_TXN_ID"));
            notificationDto.setClaimNo(rs.getInt("N_CLAIM_NO"));
            notificationDto.setInpUserId(rs.getString("V_INP_USER_ID"));
            notificationDto.setMessage(rs.getString("V_MESSAGE"));
            notificationDto.setReadStatus(rs.getString("V_READ_STATUS"));
            notificationDto.setReadDateTime(rs.getString("V_READ_DATE_TIME"));
            notificationDto.setPriorityStatus(rs.getString("V_PRIORITY_STATUS"));
            notificationDto.setNotifyDateTime(Utility.getCustomDateFormat(rs.getString("D_NOTIFY_DATE_TIME"), AppConstant.DATE_TIME_FORMAT, "d/MMM/yyyy hh:mm a"));
            notificationDto.setIpAddress(rs.getString("V_IP_ADDRESS"));
            notificationDto.setAssignUserId(rs.getString("V_ASSIGN_USER_ID"));
            notificationDto.setUrl(rs.getString("V_URL"));
            notificationDto.setRefNo(rs.getInt("N_REF_NO"));
            notificationDto.setVehicleNo(rs.getString("V_VEHICLE_NO"));
            notificationDto.setCoverNoteNo(rs.getString("V_COVER_NOTE_NO"));
            notificationDto.setAccidentDate(Utility.getCustomDateFormat(rs.getString("D_ACCIDENT_DATE"), AppConstant.DATE_FORMAT, "d/MMM/yyyy"));
            notificationDto.setCheckedStatus(rs.getString("V_CHECKED_STATUS"));
            notificationDto.setColorCode(rs.getString("V_COLOR_CODE"));
            notificationDto.setPolicyChannelType(rs.getString("V_POLICY_CHANNEL_TYPE"));
            notificationDto.setProductName(rs.getString("V_PRODUCT"));
            notificationDto.setCategoryDesc(rs.getString("V_CATEGORY_DESC"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return notificationDto;
    }

    private DocumentNotificationDto getDocumentNotificationDto(ResultSet rs) {
        DocumentNotificationDto documentNotificationDto = new DocumentNotificationDto();
        try {
            documentNotificationDto.setFormRelId(rs.getLong("form_rel_id"));
            documentNotificationDto.setDocName(rs.getString("doc_name"));
            documentNotificationDto.setDocType(rs.getInt("doc_type"));
            documentNotificationDto.setUpdateDate(Utility.getCustomDateFormat(rs.getString("time"), AppConstant.DATE_TIME_FORMAT));
            documentNotificationDto.setAssignUser(rs.getString("assign_user"));
            documentNotificationDto.setInputUser(rs.getString("inp_user"));
            documentNotificationDto.setClaimNo(rs.getInt("claim_no"));
            documentNotificationDto.setCount(rs.getInt("cnt"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return documentNotificationDto;
    }

    private DocumentNotificationDto getDocumentNotificationDtoGrouped(ResultSet rs) {
        DocumentNotificationDto documentNotificationDto = new DocumentNotificationDto();
        try {
            // For grouped results, we don't have form_rel_id, so we'll set it to 0
            documentNotificationDto.setFormRelId(0L);
            documentNotificationDto.setDocName(rs.getString("doc_name"));
            // For grouped results, we don't have a single doc_type, so we'll set it to 0
            documentNotificationDto.setDocType(0);
            documentNotificationDto.setUpdateDate(Utility.getCustomDateFormat(rs.getString("time"), AppConstant.DATE_TIME_FORMAT));
            documentNotificationDto.setAssignUser(rs.getString("assign_user"));
            documentNotificationDto.setInputUser(rs.getString("inp_user"));
            documentNotificationDto.setClaimNo(rs.getInt("claim_no"));
            documentNotificationDto.setCount(rs.getInt("cnt"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return documentNotificationDto;
    }
}
