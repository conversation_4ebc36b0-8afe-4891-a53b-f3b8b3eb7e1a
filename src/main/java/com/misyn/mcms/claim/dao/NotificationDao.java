package com.misyn.mcms.claim.dao;


import com.misyn.mcms.claim.dto.DocumentNotificationDto;
import com.misyn.mcms.claim.dto.NotificationDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

public interface NotificationDao {

    String INSERT_CLAIM_NOTIFICATION = "insert into "
            + "claim_notification ("
            + "N_CLAIM_NO,"
            + "V_INP_USER_ID,"
            + "V_MESSAGE,"
            + "V_READ_STATUS,"
            + "V_READ_DATE_TIME,"
            + "V_PRIORITY_STATUS,"
            + "D_NOTIFY_DATE_TIME,"
            + "V_IP_ADDRESS,"
            + "V_ASSIGN_USER_ID,"
            + "V_URL,"
            + "N_REF_NO,"
            + "V_VEHICLE_NO,"
            + "V_COVER_NOTE_NO,"
            + "D_ACCIDENT_DATE,"
            + "V_CHECKED_STATUS,"
            + "V_IS_MOBILE_READ,"
            + "V_COLOR_CODE,"
            + "V_POLICY_CHANNEL_TYPE,"
            + "V_PRODUCT,"
            + "V_CATEGORY_DESC"
            + ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String INSERT_MASTER_TO_HISTORY = "INSERT INTO claim_notification_history (\n" +
            "	SELECT\n" +
            "		0,\n" +
            "		N_TXN_ID,\n" +
            "		N_CLAIM_NO,\n" +
            "		V_INP_USER_ID,\n" +
            "		V_MESSAGE,\n" +
            "		V_READ_STATUS,\n" +
            "		V_READ_DATE_TIME,\n" +
            "		V_PRIORITY_STATUS,\n" +
            "		D_NOTIFY_DATE_TIME,\n" +
            "		V_IP_ADDRESS,\n" +
            "		V_ASSIGN_USER_ID,\n" +
            "		V_URL,\n" +
            "		N_REF_NO,\n" +
            "		V_VEHICLE_NO,\n" +
            "		V_COVER_NOTE_NO,\n" +
            "		D_ACCIDENT_DATE,\n" +
            "		?,\n" +//V_CHECKED_STATUS
            "		V_IS_MOBILE_READ,\n" +
            "       V_COLOR_CODE,\n" +
            "       V_POLICY_CHANNEL_TYPE,\n" +
            "       V_PRODUCT,\n" +
            "       V_CATEGORY_DESC\n" +
            "	FROM\n" +
            "		claim_notification WHERE N_TXN_ID=?\n" +
            ")";

    String UPDATE_CLAIM_NOTIFICATION = "UPDATE claim_notification SET V_READ_STATUS=?, V_READ_DATE_TIME=? WHERE N_TXN_ID=?";

    String UPDATE_CLAIM_NOTIFICATION_CHECKED = "UPDATE claim_notification_history SET V_CHECKED_STATUS=? WHERE N_TXN_ID=?";
    String DELETE_CLAIM_NOTIFICATION = "DELETE FROM  claim_notification WHERE N_TXN_ID=?";

    String SELECT_CLAIM_NOTIFICATION_BY_ASSIGN_USER_ID_LIMIT = "SELECT * FROM claim_notification WHERE V_CHECKED_STATUS NOT IN('C') AND V_ASSIGN_USER_ID = ? ORDER BY V_CHECKED_STATUS DESC,D_NOTIFY_DATE_TIME DESC  LIMIT ? ";


    String SELECT_COUNT_CLAIM_NOTIFICATION_BY_ASSIGN_USER_ID = "SELECT COUNT(N_TXN_ID) AS CNT FROM claim_notification  WHERE V_CHECKED_STATUS NOT IN('C') AND V_ASSIGN_USER_ID = ? ";

    String SELECT_COUNT_CLAIM_NOTIFICATION_BY_ASSIGN_USER_ID_AND_READ_STATUS = "SELECT COUNT(N_TXN_ID) AS CNT FROM claim_notification WHERE V_CHECKED_STATUS NOT IN('C') AND  V_ASSIGN_USER_ID = ? AND V_READ_STATUS=?";

    String SELECT_PENDING_NOTIFICATIONS = "SELECT \n" +
            "form_rel_id, GROUP_CONCAT(DISTINCT typ.V_DOC_TYPE_NAME) as doc_name, doc_type,max(update_date_time) AS time, assign_user, inp_user, claim_no,\n" +
            "COUNT(DISTINCT typ.V_DOC_TYPE_NAME) as cnt\n" +
            "FROM document_notification as doc\n" +
            "INNER JOIN claim_document_type as typ\n" +
            "ON doc.doc_type = typ.N_DOC_TYPE_ID\n" +
            "WHERE notification_send_status ='N'\n" +
            "GROUP BY form_rel_id,assign_user\n" +
            "ORDER BY form_rel_id,max(update_date_time) DESC";

    String SELECT_PENDING_NOTIFICATIONS_GROUP_BY_CLAIM_NO = "SELECT \n" +
                    "claim_no, \n" +
                    "GROUP_CONCAT(DISTINCT typ.V_DOC_TYPE_NAME) AS doc_name, \n" +
                    "MAX(update_date_time) AS time, \n" +
                    "assign_user, \n" +
                    "inp_user, \n" +
                    "COUNT(DISTINCT typ.V_DOC_TYPE_NAME) AS cnt \n" +
                    "FROM document_notification AS doc \n" +
                    "INNER JOIN claim_document_type AS typ \n" +
                    "ON doc.doc_type = typ.N_DOC_TYPE_ID \n" +
                    "WHERE notification_send_status = 'N' \n" +
                    "GROUP BY claim_no, assign_user \n" +
                    "ORDER BY claim_no, MAX(update_date_time) DESC";

    String SAVE_PENDING_NOTIFICATION = "INSERT INTO document_notification values(0,?,?,?,?,?,?,?)";

    String MARK_SENT_NOTIFICATIONS = "UPDATE document_notification SET notification_send_status = ? WHERE form_rel_id = ? AND assign_user = ?";

    String SHIFT_NOTIFICATION_ON_ACTION = "INSERT INTO claim_notification_history (" +
            " N_HIS_TXN_ID," +
            " N_TXN_ID," +
            " N_CLAIM_NO," +
            " V_INP_USER_ID," +
            " V_MESSAGE," +
            " V_READ_STATUS," +
            " V_READ_DATE_TIME," +
            " V_PRIORITY_STATUS," +
            " D_NOTIFY_DATE_TIME," +
            " V_IP_ADDRESS," +
            " V_ASSIGN_USER_ID," +
            " V_URL," +
            " N_REF_NO," +
            " V_VEHICLE_NO," +
            " V_COVER_NOTE_NO," +
            " D_ACCIDENT_DATE," +
            " V_CHECKED_STATUS," +
            " V_IS_MOBILE_READ," +
            " V_COLOR_CODE, " +
            " V_POLICY_CHANNEL_TYPE," +
            " V_PRODUCT," +
            " V_CATEGORY_DESC" +
            ")\n" +
            " (SELECT 0," +
            " N_TXN_ID," +
            " N_CLAIM_NO," +
            " V_INP_USER_ID," +
            " V_MESSAGE," +
            " 'Y'," +
            " V_READ_DATE_TIME," +
            " V_PRIORITY_STATUS," +
            " D_NOTIFY_DATE_TIME," +
            " V_IP_ADDRESS," +
            " V_ASSIGN_USER_ID," +
            " V_URL," +
            " N_REF_NO," +
            " V_VEHICLE_NO," +
            " V_COVER_NOTE_NO," +
            " D_ACCIDENT_DATE," +
            " 'C'," +
            " V_IS_MOBILE_READ," +
            " V_COLOR_CODE," +
            " V_POLICY_CHANNEL_TYPE," +
            " V_PRODUCT," +
            " V_CATEGORY_DESC" +
            " FROM claim_notification \n" +
            "WHERE \n" +
            " N_CLAIM_NO= ? and\n" +
            " V_ASSIGN_USER_ID= ?) ";

    String DELETE_SHIFTED_NOTIFICATION = "DELETE FROM claim_notification WHERE \n" +
            " N_CLAIM_NO= ? and\n" +
            " V_ASSIGN_USER_ID= ? ";

    NotificationDto insertMaster(Connection connection, NotificationDto notificationDto) throws Exception;

    NotificationDto updateMaster(Connection connection, NotificationDto notificationDto) throws Exception;

    boolean updateNotificationChecked(Connection connection, Integer txnId, String status) throws Exception;

    int insertMasterToHistory(Connection connection, Integer txnId, String status) throws Exception;

    boolean deleteNotification(Connection connection, Integer txnId) throws Exception;

    int countByAssignUserId(Connection connection, String assignUserId);

    int countByAssignUserIdAndReadStatus(Connection connection, String assignUserId, String readStatus);

    List<NotificationDto> searchAllByAssignUserIdWithLimit(Connection connection, String assignUserId, Integer limit);

    List<NotificationDto> searchMasterAllByAssignUserId(Connection connection, String assignUserId, String fromDate, String toDate, String vehicleNo, String claimNo);

    List<NotificationDto> searchHistoryAllByAssignUserId(Connection connection, String assignUserId, String fromDate, String toDate, String vehicleNo, String claimNo);

    List<DocumentNotificationDto> getPendingNotification(Connection connection);

    List<DocumentNotificationDto> getPendingNotificationGroupedByClaimNo(Connection connection);

    void savePendingNotification(Connection connection, DocumentNotificationDto documentNotificationDto) throws SQLException;

    void markSentNotifications(Connection connection, Long formId, String assignUser) throws SQLException;

    void shiftNotificationOnAction(Connection connection, Integer claimNo, UserDto user) throws Exception;

    void deleteShiftedNotification(Connection connection, Integer claimNo, UserDto user) throws Exception;
}
