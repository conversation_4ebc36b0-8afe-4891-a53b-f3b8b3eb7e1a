package com.misyn.mcms.claim.dao.impl.motorengineer;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.motorengineer.CallCenterCommonBasketDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.motorengineer.CallCenterCommonBasketDto;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CallCenterCommonBasketDaoImpl extends AbstractBaseDao<CallCenterCommonBasketDaoImpl> implements CallCenterCommonBasketDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(CallCenterCommonBasketDaoImpl.class);


    @Override
    public CallCenterCommonBasketDto insertCommonBasket(Connection connection, CallCenterCommonBasketDto dto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_CALL_CENTER_REVIEW_COMMON_BASKET)) {
            int index = 0;

            ps.setInt(++index, dto.getInspectionId());
            ps.setInt(++index, dto.getInspectionType());
            ps.setInt(++index, dto.getClaimNo());

            ps.setString(++index, dto.getRteUserId());

            // Assuming callCenterUserId and assessorId are empty on insert
            ps.setString(++index, AppConstant.EMPTY_STRING);  // call_center_user_id
            ps.setString(++index, AppConstant.EMPTY_STRING);  // assessor_id

            ps.setString(++index, AppConstant.YES);           // is_onsite_review_apply

            // Insert the enum as string name; if you want to insert code ('P','A','C'), change here
            ps.setString(++index, CallCenterCommonBasketDto.Status.PENDING.getCode());

            // Set current timestamp for submitted_datetime
            ps.setTimestamp(++index, Timestamp.valueOf(LocalDateTime.now()));

            ps.setString(++index, dto.getSubmittedUser());

            int rows = ps.executeUpdate();
            if (rows > 0) {
                return dto;
            }

        } catch (SQLException e) {
            LOGGER.error("Error inserting common basket job: {}", e.getMessage(), e);
            throw new Exception("System Error during common basket insert", e);
        }

        return null;
    }


    @Override
    public CallCenterCommonBasketDto getByInspectionId(Connection connection, String inspectionId) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SELECT_BY_INSPECTION_ID)) {
            ps.setString(1, inspectionId);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    CallCenterCommonBasketDto dto = new CallCenterCommonBasketDto();
                    dto.setId(rs.getInt("id"));
                    dto.setInspectionId(rs.getInt("inspection_id"));
                    dto.setInspectionType(rs.getInt("inspection_type"));
                    dto.setClaimNo(rs.getInt("claim_no"));
                    dto.setRteUserId(rs.getString("rte_user_id"));
                    dto.setCallCenterUserId(rs.getString("call_center_user_id"));
                    dto.setAssessorId(rs.getString("assessor_id"));
                    dto.setIsOnsiteReviewApply(rs.getString("is_onsite_review_apply"));

                    // Safely convert status string to enum, handling invalid values
                    String statusStr = rs.getString("status");
                    try {
                        dto.setStatus(CallCenterCommonBasketDto.Status.valueOf(statusStr));
                    } catch (IllegalArgumentException | NullPointerException ex) {
                        LOGGER.warn("Unknown status '{}' from DB, defaulting to PENDING", statusStr);
                        dto.setStatus(CallCenterCommonBasketDto.Status.PENDING);
                    }

                    dto.setSubmittedDatetime(rs.getString("submitted_datetime"));
                    dto.setSubmittedUser(rs.getString("submitted_user"));
                    dto.setLastUpdatedDatetime(rs.getString("last_updated_datetime"));
                    dto.setLastUpdatedUser(rs.getString("last_updated_user"));
                    return dto;
                }
            }
        } catch (SQLException e) {
            LOGGER.error("Error retrieving common basket by inspection ID: {}", e.getMessage(), e);
            throw new Exception("System Error during fetch by inspection ID", e);
        }

        return null;
    }


    @Override
    public boolean updateCallCenterUser(Connection connection, String callCenterUserId, String lastUpdatedUser, Integer inspectionId, Integer claimNo) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_CALL_CENTER_USER)) {
            int index = 0;
            ps.setString(++index, callCenterUserId);
            ps.setString(++index, lastUpdatedUser);
            ps.setInt(++index, inspectionId);
            ps.setInt(++index, claimNo);

            int rowsUpdated = ps.executeUpdate();
            return rowsUpdated > 0;
        } catch (SQLException e) {
            LOGGER.error("Error updating Call Center User: {}", e.getMessage(), e);
            throw new Exception("System Error during update Call Center User", e);
        }
    }

    @Override
    public boolean updateRteUser(Connection connection, String rteUserId, String lastUpdatedUser, Integer inspectionId) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_RTE_USER)) {
            int index = 0;
            ps.setString(++index, rteUserId);
            ps.setString(++index, lastUpdatedUser);
            ps.setInt(++index, inspectionId);

            int rowsUpdated = ps.executeUpdate();
            return rowsUpdated > 0;
        } catch (SQLException e) {
            LOGGER.error("Error updating RTE User: {}", e.getMessage(), e);
            throw new Exception("System Error during update RTE User", e);
        }
    }


    @Override
    public boolean isCallCenterUserExist(Connection connection, Integer claimNo, Integer inspectionId) throws Exception {

        try (PreparedStatement ps = connection.prepareStatement(IS_CALL_CENTER_USER_EXIST)) {
            ps.setInt(1, claimNo);
            ps.setInt(2, inspectionId);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    int count = rs.getInt(1);
                    return count > 0;
                }
                return false;
            }
        } catch (SQLException e) {
            LOGGER.error("Error checking if call center user exists: {}", e.getMessage(), e);
            throw new Exception("System error during call center user existence check", e);
        }
    }


    public boolean updateStatus(Connection connection, String statusCode, String lastUpdatedUser, Integer inspectionId, Integer claimNo) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(UPDATE_STATUS)) {
            int index = 0;
            ps.setString(++index, statusCode); // status = 'P'/'A'/'C'
            ps.setString(++index, lastUpdatedUser);
            ps.setInt(++index, inspectionId);
            ps.setInt(++index, claimNo);

            int rowsUpdated = ps.executeUpdate();
            return rowsUpdated > 0;
        } catch (SQLException e) {
            throw new Exception("Error updating status", e);
        }
    }

    @Override
    public List<CallCenterCommonBasketDto> fetchAll(Connection connection) throws Exception {
        List<CallCenterCommonBasketDto> list = new ArrayList<>();
        String query = "SELECT * FROM call_center_review_common_basket";

        try (PreparedStatement ps = connection.prepareStatement(query); ResultSet rs = ps.executeQuery()) {

            while (rs.next()) {
                CallCenterCommonBasketDto dto = new CallCenterCommonBasketDto();
                dto.setId(rs.getInt("id"));
                dto.setInspectionId(rs.getInt("inspection_id"));
                dto.setInspectionType(rs.getInt("inspection_type"));
                dto.setClaimNo(rs.getInt("claim_no"));
                dto.setRteUserId(rs.getString("rte_user_id"));
                dto.setCallCenterUserId(rs.getString("call_center_user_id"));
                dto.setAssessorId(rs.getString("assessor_id"));
                dto.setIsOnsiteReviewApply(rs.getString("is_onsite_review_apply"));
                dto.setStatus(CallCenterCommonBasketDto.Status.fromCode(rs.getString("status")));
                dto.setSubmittedDatetime(rs.getString("submitted_datetime"));
                dto.setSubmittedUser(rs.getString("submitted_user"));
                dto.setLastUpdatedDatetime(rs.getString("last_updated_datetime"));
                dto.setLastUpdatedUser(rs.getString("last_updated_user"));
                dto.setRejectedReason(rs.getString("rejected_reason"));
                dto.setRejectCount(rs.getInt("reject_count"));
                list.add(dto);
            }

        } catch (Exception e) {
            throw new Exception("Error fetching common basket list", e);
        }

        return list;
    }

    @Override
    public DataGridDto fetchOnsiteReviewList(Connection connection, List<FieldParameterDto> parameterList, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate) throws Exception {

        DataGridDto result = new DataGridDto();
        List<CallCenterCommonBasketDto> dataList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;

        StringBuilder query = new StringBuilder();
        query.append("SELECT SQL_CALC_FOUND_ROWS * FROM call_center_review_common_basket WHERE 1=1 ");

        // Filter: From Date
        if (!fromDate.isEmpty()) {
            query.append(" AND submitted_datetime >= ? ");
        }

        // Filter: To Date
        if (!toDate.isEmpty()) {
            query.append(" AND submitted_datetime <= ? ");
        }

        // Add additional dynamic filters (skip empty ones)
        List<FieldParameterDto> nonEmptyParams = new ArrayList<>();
        for (FieldParameterDto param : parameterList) {
            if (param.getFieldValue() != null && !param.getFieldValue().trim().isEmpty()) {
                query.append(" AND ").append(param.getDbFieldName()).append(" ");
                switch (param.getSearchType()) {
                    case Equal:
                        query.append("= ? ");
                        break;
                    case Like:
                        query.append("LIKE ? ");
                        break;
                }
                nonEmptyParams.add(param);
            }
        }

        // Sorting
        if (orderColumnName != null && !orderColumnName.isEmpty()) {
            query.append(" ORDER BY ").append(orderColumnName).append(" ").append(columnOrder).append(" ");
        } else {
            query.append(" ORDER BY submitted_datetime DESC ");
        }

        // Pagination
        query.append(" LIMIT ?, ? ");

        try {
            ps = connection.prepareStatement(query.toString());
            int index = 1;

            // Set parameters
            if (!fromDate.isEmpty()) {
                ps.setString(index++, fromDate);
            }
            if (!toDate.isEmpty()) {
                ps.setString(index++, toDate);
            }

            for (FieldParameterDto param : nonEmptyParams) {
                if (param.getSearchType() == FieldParameterDto.SearchType.Like) {
                    ps.setString(index++, "%" + param.getFieldValue() + "%");
                } else {
                    ps.setString(index++, param.getFieldValue());
                }
            }

            ps.setInt(index++, start);
            ps.setInt(index, length);

            rs = ps.executeQuery();

            while (rs.next()) {
                CallCenterCommonBasketDto dto = new CallCenterCommonBasketDto();
                dto.setId(rs.getInt("id"));
                dto.setInspectionId(rs.getInt("inspection_id"));
                dto.setInspectionType(rs.getInt("inspection_type"));
                dto.setClaimNo(rs.getInt("claim_no"));
                dto.setRteUserId(rs.getString("rte_user_id"));
                dto.setCallCenterUserId(rs.getString("call_center_user_id"));
                dto.setAssessorId(rs.getString("assessor_id"));
                dto.setIsOnsiteReviewApply(rs.getString("is_onsite_review_apply"));
                dto.setStatus(CallCenterCommonBasketDto.Status.fromCode(rs.getString("status")));
                dto.setSubmittedDatetime(String.valueOf(rs.getTimestamp("submitted_datetime")));
                dto.setSubmittedUser(rs.getString("submitted_user"));
                dto.setLastUpdatedDatetime(String.valueOf(rs.getTimestamp("last_updated_datetime")));
                dto.setLastUpdatedUser(rs.getString("last_updated_user"));
                dto.setRejectedReason(rs.getString("rejected_reason"));
                dto.setRejectCount(rs.getInt("reject_count"));
                dataList.add(dto);
            }

            // Get total filtered/total count
            rs = ps.executeQuery("SELECT FOUND_ROWS()");
            if (rs.next()) {
                result.setRecordsFiltered(rs.getInt(1));
                result.setRecordsTotal(rs.getInt(1));
            }

            result.setData(new ArrayList<>(dataList));


        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }

        return result;
    }

    @Override
    public boolean isAssignedUser(Connection connection, Integer claimNo, Integer inspectionId, String userId) throws Exception {
        boolean isAssigned = false;

        try (PreparedStatement ps = connection.prepareStatement(IS_ASSIGNED_TO_USER)) {
            ps.setInt(1, claimNo);
            ps.setInt(2, inspectionId);
            ps.setString(3, userId);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    isAssigned = rs.getInt(1) > 0;
                }
            }
        }

        return isAssigned;
    }


}
