package com.misyn.mcms.claim.service;

import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.motorengineer.CallCenterCommonBasketDto;

import java.util.List;

public interface CommonBasketService {

    CallCenterCommonBasketDto createCommonBasket(CallCenterCommonBasketDto dto) throws Exception;

    CallCenterCommonBasketDto findByInspectionId(String inspectionId) throws Exception;

    boolean assignCallCenterUser(String callCenterUserId, String updatedBy, Integer inspectionId, Integer claimNo) throws Exception;

    boolean assignRteUser(String rteUserId, String updatedBy, Integer inspectionId) throws Exception;

    boolean isCallCenterUserAlreadyAssigned(Integer claimNo, Integer inspectionId) throws Exception;

    boolean updateStatus(String statusCode, String updatedBy, Integer inspectionId, Integer claimNo) throws Exception;

    List<CallCenterCommonBasketDto> getAllCommonBaskets() throws Exception;

    DataGridDto getOnsiteReviewDataGridDto(List<FieldParameterDto> parameterList, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate) throws Exception;

    boolean isAssignedUser(Integer claimNo, Integer inspectionId, String userId) throws Exception;

}
