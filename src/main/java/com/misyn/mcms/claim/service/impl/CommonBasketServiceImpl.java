package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.claim.dao.impl.motorengineer.CallCenterCommonBasketDaoImpl;
import com.misyn.mcms.claim.dao.motorengineer.CallCenterCommonBasketDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;
import com.misyn.mcms.claim.dto.motorengineer.CallCenterCommonBasketDto;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.CommonBasketService;

import java.sql.Connection;
import java.util.List;

public class CommonBasketServiceImpl extends AbstractBaseService<CommonBasketServiceImpl> implements CommonBasketService {

    private final CallCenterCommonBasketDao commonBasketDao = new CallCenterCommonBasketDaoImpl();


    @Override
    public CallCenterCommonBasketDto createCommonBasket(CallCenterCommonBasketDto dto) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.insertCommonBasket(connection, dto);
        }
    }

    @Override
    public CallCenterCommonBasketDto findByInspectionId(String inspectionId) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.getByInspectionId(connection, inspectionId);
        }
    }

    @Override
    public boolean assignCallCenterUser(String callCenterUserId, String updatedBy, Integer inspectionId, Integer claimNo) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.updateCallCenterUser(connection, callCenterUserId, updatedBy, inspectionId, claimNo);
        }
    }

    @Override
    public boolean assignRteUser(String rteUserId, String updatedBy, Integer inspectionId) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.updateRteUser(connection, rteUserId, updatedBy, inspectionId);
        }
    }

    @Override
    public boolean isCallCenterUserAlreadyAssigned(Integer claimNo, Integer inspectionId) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.isCallCenterUserExist(connection, claimNo, inspectionId);
        }
    }

    @Override
    public boolean updateStatus(String statusCode, String updatedBy, Integer inspectionId, Integer claimNo) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.updateStatus(connection, statusCode, updatedBy, inspectionId, claimNo);
        }
    }

    @Override
    public List<CallCenterCommonBasketDto> getAllCommonBaskets() throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.fetchAll(connection);
        }
    }

    @Override
    public DataGridDto getOnsiteReviewDataGridDto(List<FieldParameterDto> parameterList, int start, int length, String columnOrder, String orderColumnName, String fromDate, String toDate) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.fetchOnsiteReviewList(connection, parameterList, start, length, columnOrder, orderColumnName, fromDate, toDate);
        }
    }

    @Override
    public boolean isAssignedUser(Integer claimNo, Integer inspectionId, String userId) throws Exception {
        try (Connection connection = getJDBCConnection()) {
            return commonBasketDao.isAssignedUser(connection, claimNo, inspectionId, userId);
        }
    }


}
