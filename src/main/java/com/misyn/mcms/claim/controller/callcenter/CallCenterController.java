package com.misyn.mcms.claim.controller.callcenter;

import com.google.gson.Gson;
import com.misyn.mcms.admin.UserRights;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.enums.PolicyChannelType;
import com.misyn.mcms.claim.service.*;
import com.misyn.mcms.claim.service.impl.CommonBasketServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@WebServlet(name = "CallCenterController", urlPatterns = "/CallCenter/*")
public class CallCenterController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CallCenterController.class);
    private CallCenterService callCenterService = null;
    private ClaimSuperDashboardService claimSuperDashboardService = null;
    private CommonBasketService commonBasketService = new CommonBasketServiceImpl();

    private int draw = 1;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        int type = 0;
        String pathInfo = request.getPathInfo();
        HttpSession session = request.getSession();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String formType = AppConstant.STRING_EMPTY;
        UserDto sessionUser = getSessionUser(request);
        DistrictService districtService = getDistrictServiceBySession(request);
        AssessorAllocationService assessorAllocationService = getAssessorAllocationServiceServiceBySession(request);
        callCenterService = getCallServiceBySession(request);
        AssessorService assessorService = getAssessorServiceBySession(request);
        ClaimHandlerService claimHandlerService = getCallHandlerServiceBySession(request);
        claimSuperDashboardService = getClaimSuperDashboardServiceBySession(request);

        session.setAttribute(AppConstant.CURRENT_DATE, Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));


        try {
            switch (pathInfo) {
//                case "/viewThirdPartyDetails":
//                    requestDispatcher(request,response,"/WEB-INF/jsp/claim/callcenter/assessorAllocation.jsp");
//                    break;
                case "/underWrittingDetails":
                    int policyRefNo = request.getParameter("P_POL_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_REF_NO"));
                    Integer claimNo = request.getParameter("claimNo") == null ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                    ClaimsDto claimsDto = callCenterService.getReportAccidentClaimsDto(policyRefNo, claimNo);
                    callCenterService.setOtherDetailsList(claimsDto);
                    request.setAttribute("claimsDto", claimsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policyUnderwrittingDetails.jsp");
                    break;

                case "/viewAssessorAllocation":
                    ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
                    formType = null == request.getParameter("FORM_TYPE") ? AppConstant.CLAIM_DTO_TYPE_MASTER : request.getParameter("FORM_TYPE");
                    Integer claimId = 0;
                    Integer successCode = null == request.getParameter(AppConstant.SUCCESS_CODE) ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter(AppConstant.SUCCESS_CODE));

                    if (1 == successCode) {
                        request.setAttribute(AppConstant.SUCCESS_MESSAGE, "Successfully saved");
                    }
                    if (null != sessionClaimDetails && !AppConstant.CLAIM_DTO_TYPE_HISTORY.equals(formType)) {
                        claimId = sessionClaimDetails.getClaimNo();
                        request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());
                        request.setAttribute("assessorAllocationList", assessorAllocationService.getAssessorListByClaimNo(claimId));
                        AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                        assessorAllocationDto.setClaimsDto(sessionClaimDetails);
                        String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
                        assessorAllocationDto.setAccidentTimeFileds(sysTime);
                        assessorAllocationDto.setPlaceOfinspection(null == sessionClaimDetails.getCurrentLocation() ? AppConstant.STRING_EMPTY : sessionClaimDetails.getCurrentLocation());
                        assessorAllocationDto.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                        try {
                            BigDecimal acrAmount = claimHandlerService.getReserveAmount(claimId);
                            assessorAllocationDto.setAcrAmount(acrAmount != null ? acrAmount : BigDecimal.ZERO);
                        } catch (Exception e) {
                            assessorAllocationDto.setAcrAmount(BigDecimal.ZERO);
                        }
                        request.setAttribute("assessorAllocationDto", assessorAllocationDto);
                        request.setAttribute("claimId", claimId);
                    } else {
                        claimId = null == request.getParameter("claimNo") ? 0 : Integer.parseInt(request.getParameter("claimNo"));
                        request.setAttribute(AppConstant.DISTRICT_LIST, districtService.searchAll());
                        request.setAttribute("assessorAllocationList", assessorAllocationService.getAssessorListByClaimNo(claimId));
                        AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                        assessorAllocationDto.setClaimsDto(callCenterService.search(claimId));
                        String[] sysTime = Utility.getSeparateCurruntSysTimeString12hours();
                        assessorAllocationDto.setAccidentTimeFileds(sysTime);
                        assessorAllocationDto.setPlaceOfinspection(null == assessorAllocationDto.getClaimsDto().getCurrentLocation() ? AppConstant.STRING_EMPTY : assessorAllocationDto.getClaimsDto().getCurrentLocation());
                        assessorAllocationDto.setAssignDatetime(Utility.sysDate(AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT));
                        try {
                            BigDecimal acrAmount = claimHandlerService.getReserveAmount(claimId);
                            assessorAllocationDto.setAcrAmount(acrAmount != null ? acrAmount : BigDecimal.ZERO);
                        } catch (Exception e) {
                            assessorAllocationDto.setAcrAmount(BigDecimal.ZERO);
                        }
                        request.setAttribute("assessorAllocationDto", assessorAllocationDto);
                        request.setAttribute("claimId", claimId);
                    }
                    request.setAttribute(AppConstant.INSPECTION_LIST, assessorAllocationService.getInspectionList(claimId));
                    request.setAttribute(AppConstant.RTE_LIST, assessorAllocationService.getRTEList());
                    request.setAttribute("assessorList", assessorService.getAssessorListByDivisionCode(AppConstant.STRING_EMPTY));
                    String requestedInspectionId = request.getParameter("requestedInspectionId");
                    request.setAttribute("requestedInspectionId", requestedInspectionId); //

                    boolean isOnsiteReview = Boolean.parseBoolean(request.getParameter("onsiteReview"));
                    request.setAttribute("onsiteReview", isOnsiteReview); //

                    boolean isOnsiteReviewAssignedUser = Boolean.parseBoolean(request.getParameter("isOnsiteReviewAssignedUser"));
                    request.setAttribute("isOnsiteReviewAssignedUser", isOnsiteReviewAssignedUser); //

//
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/assessorAllocation.jsp");
                    break;
                case "/viewReportAccident":
                    removeSessionClaimDetails(request, response);
                    policyRefNo = request.getParameter("P_POL_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_REF_NO"));
                    claimsDto = callCenterService.getReportAccidentClaimsDto(policyRefNo, 0);

                    claimsDto.setAccidDate(Utility.sysDate());
                    claimsDto.setAccidTime(Utility.sysDate(AppConstant.TIME_WITH_OUT_SECOND_FORMAT));
                    claimsDto.setDateOfReport(Utility.sysDate());
                    claimsDto.setTimeOfReport(Utility.sysDate(AppConstant.TIME_WITH_OUT_SECOND_FORMAT));
                    updateSessionClaimDetails(request, response, claimsDto);
                    this.lockedIntimation(request, policyRefNo, sessionUser);

                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_MASTER);

                    String latestIntimateDateStatus = getLatestClaimIntimateDateEqualsSameDayOrWithinFifteenDay(claimsDto.getPolicyDto().getLatestClmIntimDate());
                    String latestIntimateCallUser;
                    try {
                        if (null != claimsDto.getClaimHistory() && null != claimsDto.getClaimHistory().get(0)) {
                            latestIntimateCallUser = claimsDto.getClaimHistory().get(0).getCallUser();
                        } else {
                            latestIntimateCallUser = AppConstant.STRING_EMPTY;
                        }
                    } catch (Exception e) {
                        latestIntimateCallUser = AppConstant.STRING_EMPTY;
                    }

                    request.setAttribute(AppConstant.LATEST_INTIMATE_CALL_USER, latestIntimateCallUser);
                    request.setAttribute(AppConstant.LATEST_INTIMATE_DATE_STATUS, latestIntimateDateStatus);

                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/viewThirdPartyDetails":
                    Integer claims = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    claimsDto = getSessionClaimDetails(request, response);
                    if (null != claimsDto && claims.equals(claimsDto.getClaimNo())) {
                        request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_MASTER);
                    } else {
                        claimsDto = callCenterService.getViewAccidentClaimsForCallCenter(claims);
                        request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_HISTORY);
                        request.setAttribute(AppConstant.CLAIM_DTO_HISTORY, claimsDto);
                    }
                    ThirdPartyDto thirdPartyDto = getThirdPartyDtoFromMap(request, response);
                    request.setAttribute("thirdPartyDto", thirdPartyDto);
                    request.setAttribute("index", request.getParameter("index"));
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/tirdPartyVehical.jsp");
                    break;
                case "/viewReportAccidentList":
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    removeSessionType(request, response);
                    updateSessionType(request, response, type);
                    removeSessionClaimDetails(request, response);
                    this.unlockedIntimation(request, sessionUser);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policyList.jsp");
                    break;
                case "/policylist":
                    policyList(request, response);
                    break;
                case "/claimList":
                    claimList(request, response);
                    break;
                case "/onsiteReviewList":
                    onsiteReviewList(request, response);
                    break;
                case "/viewReportedClaimList":
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    removeSessionType(request, response);
                    updateSessionType(request, response, type);
                    removeSessionClaimDetails(request, response);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/claimList.jsp");
                    break;
                case "/viewOnsiteReviewList":
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    removeSessionType(request, response);
                    updateSessionType(request, response, type);
                    removeSessionClaimDetails(request, response);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/onsiteReviewClaimList.jsp");
                    break;

                case "/getTableValueFromGivenCriteria":
                    getTableValueFromGivenCriteria(request, response);
                    break;
                case "/searchVehicle":
                    isAvailableVehicleOnPolicyInfo(request, response);
                    break;
                case "/checkAccidentDate":
                    checkAccidentDate(request, response);
                    break;

                case "/viewDamagePart":
                    String fortmType = request.getParameter(AppConstant.FORM_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.FORM_TYPE);
                    Integer vehClsId = request.getParameter("vehClsId") == null ? 0 : Integer.parseInt(request.getParameter("vehClsId"));
                    if ("HISTORY".equalsIgnoreCase(fortmType)) {
                        claimsDto = (ClaimsDto) session.getAttribute(AppConstant.CLAIM_DTO_HISTORY);
                    } else {
                        claimsDto = getSessionClaimDetails(request, response);
                    }
                    List<DamageBodyPartDto> damageList = callCenterService.getDamageBodyPartDtoList(claimsDto.getClaimNo(), vehClsId);
                    request.setAttribute("damageList", damageList);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/viewDamagePart.jsp");
                    break;
                case "/viewVehicleModel":
                    String vehicleModelUrl = "#";
                    vehClsId = request.getParameter("vehClsId") == null ? 0 : Integer.parseInt(request.getParameter("vehClsId"));
                    switch (vehClsId) {
                        case 1:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/car_model_image.jsp";
                            break;
                        case 2:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/van_model_image.jsp";
                            break;
                        case 3:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/jeep_model_image.jsp";
                            break;
                        case 4:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/bus_model_image.jsp";
                            break;
                        case 5:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/doublecab_model_image.jsp";
                            break;
                        case 6:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/motorbike_model_image.jsp";
                            break;
                        case 7:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/lorry_model_image.jsp";
                            break;
                        case 8:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/threeweeler_model_image.jsp";
                            break;
                        case 9:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/tractor_model_image.jsp";
                            break;
                        case 10:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/doublecab_model_image.jsp";
                            break;
                        case 11:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/doublecab_model_image.jsp";
                            break;
                        case 12:
                            vehicleModelUrl = "/WEB-INF/jsp/claim/callcenter/vehicle_models/car_model_image.jsp";
                            break;
                    }
                    requestDispatcher(request, response, vehicleModelUrl);
                    break;
                case "/viewReportClaim":
                    //  policyRefNo = request.getParameter("P_POL_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_REF_NO"));
                    claims = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    claimsDto = callCenterService.getReportAccidentClaimsDtoByClaimNo(claims);

                    updateSessionClaimDetails(request, response, claimsDto);
                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_MASTER);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/viewOnsiteReview":
                case "/viewClaim":
                    removeSessionClaimDetails(request, response);
                    setMenuAccess(session);
                    claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    type = request.getParameter("type") == null ? 0 : Integer.parseInt(request.getParameter("type"));
                    claimsDto = callCenterService.getViewAccidentClaimsForCallCenter(claimId);
                    ClaimHandlerDto claimHandlerDto = claimHandlerService.searchClaimByClaimNo(claimId);
                    boolean isTheft;
                    UserDto user = getSessionUser(request);
                    isTheft = claimsDto.getCauseOfLoss().equals(AppConstant.THEFT) && AppConstant.ACCESS_LEVEL_CALL_CENTER.contains("'".concat(String.valueOf(user.getAccessUserType())).concat("'"));
                    Integer tabIndex = Integer.parseInt(request.getParameter(AppConstant.P_TAB_INDEX) == null ? AppConstant.ZERO : request.getParameter(AppConstant.P_TAB_INDEX));
                    claimsDto.setFollowCallContactPersonTitle(claimsDto.getReporterTitle());
                    claimsDto.setFollowCallContactPersonName(claimsDto.getReporterName());
                    claimsDto.setFollowCallContactNumber(claimsDto.getCliNo());
                    request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    request.setAttribute(AppConstant.TAB_INDEX, tabIndex);
                    request.setAttribute(AppConstant.CLAIM_HANDLER_DTO, claimHandlerDto);

                    String requestedInspectionId2 = request.getParameter("requestedInspectionId");
                    request.setAttribute("requestedInspectionId", requestedInspectionId2); //

                    boolean isOnsiteReview2 = Boolean.parseBoolean(request.getParameter("onsiteReview"));
                    request.setAttribute("onsiteReview", isOnsiteReview2); //

                    if(isOnsiteReview2){
                        boolean onsiteReviewAssignedUser = commonBasketService.isAssignedUser(claimId, Integer.valueOf(requestedInspectionId2), user.getUserId());
                        request.setAttribute("isOnsiteReviewAssignedUser", onsiteReviewAssignedUser);
                    }

                    updateSessionClaimDetails(request, response, claimsDto);
                    if (type == 4) {
                        if (null != claimsDto.getPolicyDto() && null != claimsDto.getPolicyDto().getPolicyRefNo()) {
                            updateMappedPolicyNo(request, response, claimsDto.getPolicyDto().getPolicyRefNo());
                        }
                    }

                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_MASTER);
                    request.setAttribute(AppConstant.IS_THEFT, isTheft);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/viewClaimHistory":
//                    removeSessionClaimDetails(request, response);
                    claimId = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    claimsDto = callCenterService.getViewAccidentClaimsForCallCenter(claimId);

                    request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_HISTORY);

                    session.removeAttribute(AppConstant.CLAIM_DTO_HISTORY);
                    session.setAttribute(AppConstant.CLAIM_DTO_HISTORY, claimsDto);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;
                case "/policyMap":
                    policyRefNo = request.getParameter("P_POL_REF_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_POL_REF_NO"));
                    PolicyDto policy = callCenterService.getPolicyDetails(policyRefNo);
                    claimsDto = new ClaimsDto();
                    if (null != policy.getVehicleNumber()) {
                        claimsDto.setClaimHistory(callCenterService.getClaimHistoryForPolicyRefNo(policy.getVehicleNumber()));
                    }
                    claimsDto.setPolicyDto(policy);
                    request.setAttribute(AppConstant.SESSION_CLAIM_DTO, claimsDto);
                    updateSessionPolNo(request, response, policyRefNo);
                    request.setAttribute(AppConstant.policyRefNo, policyRefNo);
                    request.setAttribute(AppConstant.SESSION_TYPE, request.getParameter("TYPE"));
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policyView.jsp");
                    break;
                case "/setToSession":
                    policyRefNo = getSessionPolicyNumber(request) == null ? 0 : getSessionPolicyNumber(request);
                    claimsDto = getSessionClaimDetails(request, response);
                    claimsDto.setPolRefNo(policyRefNo);
                    removeSessionClaimDetails(request, response);
                    String isMap;

                    if (AppConstant.COVER_NOTE.equalsIgnoreCase(claimsDto.getPolicyDto().getPolicyNumber().substring(0, 5))) {
                        isMap = AppConstant.YES;
                    } else {
                        isMap = AppConstant.NO;
                    }

                    claimsDto = callCenterService.getViewAccidentClaimsDto(claimsDto.getClaimNo(), policyRefNo);

                    if (AppConstant.COVER_NOTE.equalsIgnoreCase(claimsDto.getPolicyDto().getPolicyNumber().substring(0, 5))) {
                        isMap = AppConstant.NO;
                    }
                    updateSessionClaimDetails(request, response, claimsDto);
                    request.setAttribute(AppConstant.FORM_TYPE, AppConstant.CLAIM_DTO_TYPE_MASTER);
                    request.setAttribute(AppConstant.ISMAP, isMap);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policy.jsp");
                    break;

                case "/viewReportedClaim":
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    updateSessionType(request, response, type);
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/policyList.jsp");
                    break;

                case "/replaceMapPolicy":
                    type = request.getParameter(AppConstant.SESSION_TYPE) == null ? 0 : Integer.parseInt(request.getParameter(AppConstant.SESSION_TYPE));
                    ErrorMessageDto errorMessageDto = updatePolicyDetails(request, response);
                    updateSessionType(request, response, type);
                    json = gson.toJson(errorMessageDto);
                    printWriter(request, response, json);
                    break;
                case "/viewSpecialRemarks":
                    claimsDto = getSessionClaimDetails(request, response);
                    claims = request.getParameter("P_N_CLIM_NO") == null ? 0 : Integer.parseInt(request.getParameter("P_N_CLIM_NO"));
                    formType = null == request.getParameter("FORM_TYPE") ? AppConstant.CLAIM_DTO_TYPE_MASTER : request.getParameter("FORM_TYPE");
                    if (claimsDto == null) {
                        claimsDto = callCenterService.search(claims);
                    }

                    if (!AppConstant.CLAIM_DTO_TYPE_HISTORY.equals(formType)) {
                        claims = claimsDto.getClaimNo();
                    }

                    callCenterService.setSpecialRemarkDtoList(claimsDto, claims);
                    request.setAttribute("remarkList", claimsDto.getRemarkList());
                    requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/specialRemarks.jsp");
                    break;
                case "/coverNote":
                    PolicyDto policyDto = new PolicyDto();
                    sessionUser = getSessionUser(request);
                    policyDto.setVehicleNumber(request.getParameter("P_VEH_NO") == null ? AppConstant.STRING_EMPTY : request.getParameter("P_VEH_NO"));
                    policyDto.setCoverNoteNo(request.getParameter("P_REF_NO") == null ? AppConstant.STRING_EMPTY : request.getParameter("P_REF_NO"));
                    policyDto.setCustName(request.getParameter("P_NAME") == null ? AppConstant.STRING_EMPTY : request.getParameter("P_NAME"));
                    policyDto.setChassisNo(request.getParameter("P_CHASSIS_NO") == null ? AppConstant.STRING_EMPTY : request.getParameter("P_CHASSIS_NO"));
                    policyDto.setPolicyChannelType(null == request.getParameter("POLICY_CHANNEL_TYPE") || request.getParameter("POLICY_CHANNEL_TYPE").trim().isEmpty() ? PolicyChannelType.CONVENTIONAL.name() : request.getParameter("POLICY_CHANNEL_TYPE"));
                    ErrorMessageDto messageDto = new ErrorMessageDto();
                    try {
                        claimsDto = callCenterService.savePolicyCoverNote(policyDto, sessionUser);

                        if (claimsDto.getPolicyDto().getPolicyRefNo() > 0) {
                            messageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
                            messageDto.setMessage("Success!");
                            messageDto.setDtoFieldName(claimsDto.getPolicyDto().getPolicyRefNo().toString());
                            returnJson(messageDto, response);
                        } else {
                            messageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                            messageDto.setMessage("Failed, Error Occurred!");
                            returnJson(messageDto, response);
                        }
                    } catch (Exception e) {
                        messageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
                        messageDto.setMessage("failed, Error Occurred!");
                        returnJson(messageDto, response);
                    }
                    break;
                case "/updateISF":
                    updateISF(request, response);
                    break;
                case "/markPriority":
                    markPriority(request, response);
                    break;
                case "/viewClaimWorkflow":
                    viewClaimWorkflow(request, response);
                    break;
                case "/theftAndFound":
                    markAsTheftAndFound(request, response);
                    break;
                case "/isTheft":
                    isTheftClaim(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void setMenuAccess(HttpSession session) {
        // need to change in production
        String menuId = "17";
        String subMenuId = "4";
        // need to change in production
        String key = menuId + AppConstant.STRING_EMPTY + subMenuId;
        Map<String, UserRights> userRightMap = (Map<String, UserRights>) session.getAttribute(AppConstant.G_MENU_ITEM);
        if (userRightMap != null) {
            UserRights userRight = userRightMap.get(key);
            if (userRight != null) {
                session.setAttribute(AppConstant.RIGHT_I, new String(userRight.getV_input()));
                session.setAttribute(AppConstant.RIGHT_M, new String(userRight.getV_modify()));
                session.setAttribute(AppConstant.RIGHT_D, new String(userRight.getV_delete()));
                session.setAttribute(AppConstant.RIGHT_A1, new String(userRight.getV_auth1()));
                session.setAttribute(AppConstant.RIGHT_A2, new String(userRight.getV_auth2()));
                session.setAttribute(AppConstant.USER_RIGHT, userRight);
                session.setAttribute(AppConstant.SELECT_MENU_NAME, userRight.getV_mnuname());
                session.setAttribute(AppConstant.SELECT_SUB_MENU_NAME, userRight.getV_itmname());
            }
        }
    }

    private void isTheftClaim(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        Gson gson = new Gson();
        String json;
        try {
            boolean isTheft = callCenterService.isTheftClaim(claimNo);
            json = gson.toJson(isTheft ? "Y" : "N");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void markAsTheftAndFound(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("N_CLAIM_NO") || request.getParameter("N_CLAIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.parseInt(request.getParameter("N_CLAIM_NO"));
        String remark = null == request.getParameter("V_REMARK") ? AppConstant.STRING_EMPTY : request.getParameter("V_REMARK");
        UserDto user = getSessionUser(request);
        String json;
        Gson gson = new Gson();
        try {
            boolean isMarked = callCenterService.markAsTheftAndFound(claimNo, remark, user);
            json = gson.toJson(isMarked ? "SUCCESS" : "FAIL");
            printWriter(request, response, json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            json = gson.toJson("ERROR");
            printWriter(request, response, json);
        }
    }

    private void viewClaimWorkflow(HttpServletRequest request, HttpServletResponse response) {
        Integer claimId = null == request.getParameter("P_N_CLIM_NO") || request.getParameter("P_N_CLIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("P_N_CLIM_NO"));
        try {
            ClaimSuperDashboardDto claimSuperDashboardDto = claimSuperDashboardService.getClaimSuperDashboardDto(claimId);
            request.setAttribute(AppConstant.CLAIM_SUPER_DASHBOARD_DTO, claimSuperDashboardDto);
            requestDispatcher(request, response, "/WEB-INF/jsp/claim/callcenter/claimWorkflow.jsp");
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void markPriority(HttpServletRequest request, HttpServletResponse response) {
        Integer claimNo = null == request.getParameter("P_N_CLIM_NO") || request.getParameter("P_N_CLIM_NO").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("P_N_CLIM_NO"));
        Integer priority = null == request.getParameter("priority") || request.getParameter("priority").isEmpty() ? AppConstant.ZERO_INT : Integer.valueOf(request.getParameter("priority"));
        String remark = null == request.getParameter("priorityRemark") || request.getParameter("priorityRemark").isEmpty() ? AppConstant.STRING_EMPTY : request.getParameter("priorityRemark");
        UserDto user = getSessionUser(request);
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();

        try {
            callCenterService.markPriority(claimNo, priority, remark, user);
            PrintWriter out = response.getWriter();
            errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
            errorMessageDto.setMessage(AppConstant.SUCCESS);
            errorMessageDto.setDtoFieldName(claimNo.toString());
            returnJson(errorMessageDto, response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage(AppConstant.FAIL);
            returnJson(errorMessageDto, response);
        }
    }

    private String getLatestClaimIntimateDateEqualsSameDayOrWithinFifteenDay(String latestClmIntimDate) {

        if (null != latestClmIntimDate && !latestClmIntimDate.equals(AppConstant.STRING_EMPTY)) {
            long days = Utility.getNoDaysDateDiff(latestClmIntimDate, AppConstant.DATE_FORMAT);

            if (0 == days) {
                return "0";
            } else if (15 >= days) {
                return "1";
            } else {
                return "2";
            }
        }
        return null;
    }

    private void updateISF(HttpServletRequest request, HttpServletResponse response) {
        String type = request.getParameter("type");
        ClaimsDto claimsDto = getSessionClaimDetails(request, response);

        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        ClaimsDto claimsDto1 = new ClaimsDto();
        try {
            BeanUtils.populate(claimsDto, request.getParameterMap());
            claimsDto.setAccidDate(Utility.getCustomDateFormat(claimsDto.getAccidDate(), AppConstant.DATE_FORMAT, AppConstant.DATE_FORMAT));
            claimsDto.setAccidTime(Utility.getCustomDateFormat(claimsDto.getAccidTime(), AppConstant.TIME_WITH_OUT_SECOND_FORMAT, AppConstant.TIME_FORMAT));
            claimsDto.setDateOfReport(Utility.getCustomDateFormat(claimsDto.getDateOfReport(), AppConstant.DATE_FORMAT, AppConstant.DATE_FORMAT));
            claimsDto.setTimeOfReport(Utility.getCustomDateFormat(claimsDto.getTimeOfReport(), AppConstant.TIME_WITH_OUT_SECOND_FORMAT, AppConstant.TIME_FORMAT));

            callCenterService.updateIsfClaim(claimsDto);

            errorMessageDto.setErrorCode(AppConstant.NO_ERRORS_CODE);
            errorMessageDto.setMessage("Success!");
            errorMessageDto.setDtoFieldName(claimsDto.getClaimNo().toString());
            returnJson(errorMessageDto, response);
        } catch (Exception e) {
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            errorMessageDto.setMessage("failed, Error Occurred!");
            returnJson(errorMessageDto, response);
            LOGGER.error(e.getMessage());
        }
    }


    private void checkAccidentDate(HttpServletRequest request, HttpServletResponse response) {
        String resp = "NO";
        try {
            String policyRefNoString = request.getParameter("policyRefNo");
            String accidentDate = Utility.getCustomDateFormat(request.getParameter("accidentDate"), AppConstant.DATE_TIME_WITH_OUT_SECOND_FORMAT, AppConstant.DATE_FORMAT);

            if (null != accidentDate && !accidentDate.equals("") && null != policyRefNoString && !policyRefNoString.equals("")) {
                PolicyDto policyDto = callCenterService.searchPolicyByValidPolicyDate(Integer.parseInt(policyRefNoString), accidentDate);
                if (policyDto.getPolicyRefNo() > 0)
                    resp = "YES";

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        returnJson(resp, response);
    }

    private void isAvailableVehicleOnPolicyInfo(HttpServletRequest request, HttpServletResponse response) {
        String resp = "NO";
        try {
            String vehicleNo = request.getParameter("vehicleNo");
            if (null != vehicleNo && !vehicleNo.equals("")) {
                vehicleNo = vehicleNo.trim();
                PolicyDto policyDto = callCenterService.searchPolicyByVehicleNo(vehicleNo);
                if (policyDto.getPolicyRefNo() > 0)
                    resp = "YES";

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        returnJson(resp, response);
    }

    private ThirdPartyDto getThirdPartyDtoFromMap(HttpServletRequest request, HttpServletResponse response) {
        String index = request.getParameter("index");
        if (index != null && !index.isEmpty()) {
            ClaimsDto sessionClaimDetails = getSessionClaimDetails(request, response);
            Map<Integer, ThirdPartyDto> thirdPartyDtoMap = sessionClaimDetails.getThirdPartyDtoMap();
            ThirdPartyDto thirdPartyDto = thirdPartyDtoMap.get(Integer.parseInt(index));
            return thirdPartyDto;
        } else
            return new ThirdPartyDto();
    }

    private void policyList(HttpServletRequest request, HttpServletResponse response) {
        String INSPEC_DATE = String.valueOf(Integer.parseInt(Utility.sysDate("yyyy")) - 3).concat("-01-01");
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json;
        String policyNo = request.getParameter("txtPolNumber") == null ? AppConstant.STRING_EMPTY : request.getParameter("txtPolNumber");
        String vehicleNumber = request.getParameter("txtVehicleNumber") == null ? AppConstant.STRING_EMPTY : request.getParameter("txtVehicleNumber");
        String refNumber = request.getParameter("txtRefNumber") == null ? AppConstant.STRING_EMPTY : request.getParameter("txtRefNumber");
        String insuredName = request.getParameter(AppConstant.TXT_INSURED_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSURED_NAME);
        String engineNo = request.getParameter(AppConstant.TXT_ENGINE_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ENGINE_NO);
        String insuredNic = request.getParameter(AppConstant.TXT_INSURED_NIC) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSURED_NIC);
        String chassisNo = request.getParameter(AppConstant.TXT_CHASSIS_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CHASSIS_NO);
        String policyStatus = request.getParameter(AppConstant.TXT_POLICY_STATUS) == null ? AppConstant.INFORCE : request.getParameter(AppConstant.TXT_POLICY_STATUS);
        String policyChannelType = request.getParameter(AppConstant.POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.POLICY_CHANNEL_TYPE);
        String accidentDate = request.getParameter("accidentDate") == null ? AppConstant.STRING_EMPTY : request.getParameter("accidentDate");

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("V_CUST_NAME", insuredName, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("V_VEHICLE_NUMBER", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("V_COVER_NOTE_NO", refNumber, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_CHASSIS_NO", chassisNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_ENGINE_NO", engineNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_CUST_NIC", insuredNic, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("V_POL_STATUS", AppConstant.POL_DELETE, FieldParameterDto.SearchType.NOT_Equal, parameterList);
            this.addFieldParameter("V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);
            if (!"0".equalsIgnoreCase(policyStatus)) {
                this.addFieldParameter("V_POL_STATUS", policyStatus, FieldParameterDto.SearchType.Equal, parameterList);
            }
            //    this.addFieldParameter("D_INSPEC_DATE", INSPEC_DATE, FieldParameterDto.SearchType.Equal_And_Greater_Than, parameterList);
            switch (orderColumnName) {
                case "policyRefNo":
                    orderColumnName = "N_POL_REF_NO";
                    //  orderColumnName = " D_INSPEC_DATE DESC, N_REN_COUNT DESC,N_END_COUNT ";
                    break;
                case "policyNumber":
                    orderColumnName = "V_POL_NUMBER";
                    //   orderColumnName = " V_POL_NUMBER DESC, N_REN_COUNT DESC,N_END_COUNT";
                    break;
                case "endCount":
                    orderColumnName = "N_END_COUNT";
                    break;
                case "renCount":
                    orderColumnName = "N_REN_COUNT";
                    break;
                case "vehicleNumber":
                    orderColumnName = "V_VEHICLE_NUMBER";
                    break;
                case "chassisNo":
                    orderColumnName = "V_CHASSIS_NO";
                    break;
                case "coverNoteNo":
                    orderColumnName = "V_COVER_NOTE_NO";
                    break;
                case "custName":
                    orderColumnName = "V_CUST_NAME";
                    break;
                case "latestClmIntimDate":
                    orderColumnName = "D_LATEST_CLM_INTIM_DATE";
                    break;
                case "expireDate":
                    orderColumnName = "D_EXPIRE_DATE";
                    break;
                case "inspecDate":
                    orderColumnName = "D_INSPEC_DATE";
                    break;
                case "policyChannelType":
                    orderColumnName = "V_POLICY_CHANNEL_TYPE";
                    break;
                case "polStatus":
                    orderColumnName = "V_POL_STATUS";
                    break;
            }

            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_NO, policyNo);
            request.getSession().setAttribute(AppConstant.SEARCH_CHASSIS_NO, chassisNo);
            request.getSession().setAttribute(AppConstant.SEARCH_ENGINE_NO, engineNo);
            request.getSession().setAttribute(AppConstant.SEARCH_REF_NUMBER, refNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_VEHICLE_NUMBER, vehicleNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_INSURED_NAME, insuredName);
            request.getSession().setAttribute(AppConstant.SEARCH_INSURED_NIC, insuredNic);
            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_STATUS, policyStatus);

            DataGridDto data = callCenterService.getPolicyDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, accidentDate, vehicleNumber);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }

    }

    private void claimList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = AppConstant.STRING_EMPTY;
        String fromDate = request.getParameter(AppConstant.TXT_FROM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FROM_DATE);
        String toDate = request.getParameter(AppConstant.TXT_TO_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_TO_DATE);
        String claimNumber = request.getParameter(AppConstant.TXT_CLAIM_DATE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLAIM_DATE);
        String policyNo = request.getParameter(AppConstant.TXT_POL_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POL_NUMBER);
        String status = request.getParameter(AppConstant.TXT_V_STATUS) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_V_STATUS);
        String location = request.getParameter(AppConstant.TXT_LOCATION) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_LOCATION);
        String coverNoteNo = request.getParameter(AppConstant.TXT_REF_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_REF_NUMBER);
        String vehicleNumber = request.getParameter(AppConstant.TXT_VEHICLE_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_VEHICLE_NUMBER);
        String insuredName = request.getParameter(AppConstant.TXT_INSURED_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSURED_NAME);
        String engineNo = request.getParameter(AppConstant.TXT_ENGINE_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ENGINE_NO);
        String insuredNic = request.getParameter(AppConstant.TXT_INSURED_NIC) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_INSURED_NIC);
        String chassisNo = request.getParameter(AppConstant.TXT_CHASSIS_NO) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CHASSIS_NO);
        String followupCallDone = request.getParameter(AppConstant.TXT_FOLLOWUP_CALL_DONE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_FOLLOWUP_CALL_DONE);
        String callUserName = request.getParameter(AppConstant.TXT_CALL_USER_NAME) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CALL_USER_NAME);
        String cliNo = request.getParameter(AppConstant.TXT_CLI_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_CLI_NUMBER);
        String otherContNo = request.getParameter(AppConstant.TXT_OTHER_CONTACT_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_OTHER_CONTACT_NUMBER);
        String policyChannelType = request.getParameter(AppConstant.TXT_POLICY_CHANNEL_TYPE) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_POLICY_CHANNEL_TYPE);
        String isfClaimNo = request.getParameter(AppConstant.TXT_ISF_CLAIM_NUMBER) == null ? AppConstant.STRING_EMPTY : request.getParameter(AppConstant.TXT_ISF_CLAIM_NUMBER);

        try {
            int start = Integer.parseInt(request.getParameter(AppConstant.START));
            int length = Integer.parseInt(request.getParameter(AppConstant.LENGTH));
            String columnIndex = request.getParameter(AppConstant.COLUMN_INDEX);
            String columnOrder = request.getParameter(AppConstant.COLUMN_ORDER);
            String orderColumnName = request.getParameter(AppConstant.TAG_START_ORDER_COLUMN_NAME_DATA + columnIndex + AppConstant.TAG_END_ORDER_COLUMN_NAME_DATA);

            this.addFieldParameter("t1.N_CLIM_NO", claimNumber, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(policyNo)) {
                this.addFieldParameter("t1.V_POL_NUMBER_LAST_DIGIT", getPolicyNumberLastDigit(policyNo), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_POL_NUMBER", policyNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!AppConstant.STRING_EMPTY.equalsIgnoreCase(vehicleNumber)) {
                this.addFieldParameter("t1.V_VEHICLE_NO_LAST_DIGIT", getVehicleNumberLastDigit(vehicleNumber), FieldParameterDto.SearchType.Equal, parameterList);
            }
            this.addFieldParameter("t1.V_VEHICLE_NO", vehicleNumber, FieldParameterDto.SearchType.Like, parameterList);

            this.addFieldParameter("t1.V_COVER_NOTE_NO", coverNoteNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_CUST_NAME", insuredName, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_CUST_NIC", insuredNic, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_ENGINE_NO", engineNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t3.V_CHASSIS_NO", chassisNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_PLACE_OF_ACCID", location, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_CALL_USER", callUserName, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_CLI_NO", cliNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_OTHER_CONT_NO", otherContNo, FieldParameterDto.SearchType.Like, parameterList);
            this.addFieldParameter("t1.V_ISF_CLAIM_NO", isfClaimNo, FieldParameterDto.SearchType.Like, parameterList);

            if (!"0".equals(status)) {
                this.addFieldParameter("t1.N_CLAIM_STATUS", status, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!"0".equals(followupCallDone)) {
                this.addFieldParameter("t1.V_IS_FOLLOWUP_CALL_DONE", followupCallDone, FieldParameterDto.SearchType.Equal, parameterList);
            }
            if (!"0".equals(policyChannelType)) {
                this.addFieldParameter("t1.V_POLICY_CHANNEL_TYPE", policyChannelType, FieldParameterDto.SearchType.Equal, parameterList);
            }

            switch (orderColumnName) {
                case "refNo":
                    orderColumnName = "t1.N_REF_NO";
                    break;
                case "claimNo":
                    orderColumnName = "t1.N_CLIM_NO";
                    break;
                case "policyNumber":
                    orderColumnName = "t1.V_POL_NUMBER";
                    break;
                case "vehicleNo":
                    orderColumnName = "v_vehicle_number";
                    break;
                case "callUser":
                    orderColumnName = "t1.V_CALL_USER";
                    break;
                case "dateOfReport":
                    orderColumnName = "t1.D_DATE_OF_REPORT";
                    break;
                case "timeOfReport":
                    orderColumnName = "t1.T_TIME_OF_REPORT";
                    break;
                case "coverNoteNo":
                    orderColumnName = "t1.V_COVER_NOTE_NO";
                    break;
                case "reporterName":
                    orderColumnName = "t1.V_REPORTER_NAME";
                    break;
                case "accidDate":
                    orderColumnName = "t1.D_ACCID_DATE";
                    break;
                case "accidTime":
                    orderColumnName = "t1.T_ACCID_TIME";
                    break;
                case "placeOfAccid":
                    orderColumnName = "t1.V_PLACE_OF_ACCID";
                    break;
                case "claimStatusDesc":
                    orderColumnName = "t2.v_status_desc";
                    break;
                case "chassisNo":
                    orderColumnName = "t3.V_CHASSIS_NO";
                    break;
                case "cliNo":
                    orderColumnName = "t1.V_CLI_NO";
                    break;
//                case "otherContNo":
//                    orderColumnName = "t1.V_OTHER_CONT_NO";
//                    break;
                case "policyChannelType":
                    orderColumnName = "t1.V_POLICY_CHANNEL_TYPE";
                    break;
                case "isfClaimNo":
                    orderColumnName = "t1.V_ISF_CLAIM_NO";
                    break;
            }

            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_NO, policyNo);
            request.getSession().setAttribute(AppConstant.SEARCH_CHASSIS_NO, chassisNo);
            request.getSession().setAttribute(AppConstant.SEARCH_ENGINE_NO, engineNo);
            request.getSession().setAttribute(AppConstant.SEARCH_REF_NUMBER, coverNoteNo);
            request.getSession().setAttribute(AppConstant.SEARCH_VEHICLE_NUMBER, vehicleNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_INSURED_NAME, insuredName);
            request.getSession().setAttribute(AppConstant.SEARCH_INSURED_NIC, insuredNic);

            request.getSession().setAttribute(AppConstant.SEARCH_CLAIM_NUMBER, claimNumber);
            request.getSession().setAttribute(AppConstant.SEARCH_FROM_DATE, fromDate);
            request.getSession().setAttribute(AppConstant.SEARCH_TO_DATE, toDate);
            request.getSession().setAttribute(AppConstant.SEARCH_CLAIM_STATUS, status);
            request.getSession().setAttribute(AppConstant.SEARCH_FOLLOWUP_CALL_DONE, followupCallDone);
            request.getSession().setAttribute(AppConstant.SEARCH_USER_NAME, callUserName);
            request.getSession().setAttribute(AppConstant.SEARCH_CLI_NUMBER, cliNo);
            request.getSession().setAttribute(AppConstant.SEARCH_OTHER_CONTACT_NUMBER, otherContNo);
            request.getSession().setAttribute(AppConstant.SEARCH_POLICY_CHANNEL_TYPE, policyChannelType);
            request.getSession().setAttribute(AppConstant.SEARCH_ISF_CLAIM_NUMBER, isfClaimNo);

            DataGridDto data = callCenterService.getClaimDataGridDto(parameterList, draw++, start, length, columnOrder, orderColumnName, fromDate, toDate);
            json = gson.toJson(data);
            PrintWriter out = response.getWriter();
            out.print(json);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
    private void onsiteReviewList(HttpServletRequest request, HttpServletResponse response) {
        List<FieldParameterDto> parameterList = new ArrayList<>();
        Gson gson = new Gson();
        String json = "";

        try {
            String fromDate = request.getParameter("txtFromDate");
            String toDate = request.getParameter("txtToDate");
            String claimNumber = request.getParameter("txtClaimNumber");
            String status = request.getParameter("txtStatus");

            fromDate = fromDate == null ? "" : fromDate;
            toDate = toDate == null ? "" : toDate;
            claimNumber = claimNumber == null ? "" : claimNumber;
            status = status == null ? "" : status;

            int start = Integer.parseInt(request.getParameter("start"));
            int length = Integer.parseInt(request.getParameter("length"));
            String columnIndex = request.getParameter("order[0][column]");
            String columnOrder = request.getParameter("order[0][dir]");
            String orderColumnName = request.getParameter("columns[" + columnIndex + "][data]");

            // Search filters
            if (!claimNumber.isEmpty()) {
                this.addFieldParameter("t1.claim_no", claimNumber, FieldParameterDto.SearchType.Equal, parameterList);

            }

            if (status.equals("P") || status.equals("A") || status.equals("C")) {
                this.addFieldParameter("status", status, FieldParameterDto.SearchType.Equal, parameterList);
            }

            // Sorting column fix (matching column names in DB)
            switch (orderColumnName) {
                case "id": orderColumnName = "id"; break;
                case "inspectionId": orderColumnName = "inspection_id"; break;
                case "inspectionType": orderColumnName = "inspection_type"; break;
                case "claimNo": orderColumnName = "claim_no"; break;
                case "rteUserId": orderColumnName = "rte_user_id"; break;
                case "callCenterUserId": orderColumnName = "call_center_user_id"; break;
                case "assessorId": orderColumnName = "assessor_id"; break;
                case "isOnsiteReviewApply": orderColumnName = "is_onsite_review_apply"; break;
                case "status": orderColumnName = "status"; break;
                case "submittedDatetime": orderColumnName = "submitted_datetime"; break;
                case "submittedUser": orderColumnName = "submitted_user"; break;
                case "lastUpdatedDatetime": orderColumnName = "last_updated_datetime"; break;
                case "lastUpdatedUser": orderColumnName = "last_updated_user"; break;
                case "rejectedReason": orderColumnName = "rejected_reason"; break;
                case "rejectCount": orderColumnName = "reject_count"; break;
                default: orderColumnName = "id"; break;
            }

            // Store search session values if needed
            request.getSession().setAttribute("searchClaimNumber", claimNumber);
            request.getSession().setAttribute("searchStatus", status);
            request.getSession().setAttribute("searchFromDate", fromDate);
            request.getSession().setAttribute("searchToDate", toDate);

            // Call service method
            DataGridDto dataGrid = commonBasketService.getOnsiteReviewDataGridDto(
                    parameterList, start, length, columnOrder, orderColumnName, fromDate, toDate
            );

            json = gson.toJson(dataGrid);
            response.setContentType("application/json");
            PrintWriter out = response.getWriter();
            out.print(json);

        } catch (Exception e) {
            LOGGER.error("Error in onsiteReviewList: " + e.getMessage(), e);
        }
    }


    private ErrorMessageDto updatePolicyDetails(HttpServletRequest request, HttpServletResponse response) {
        ErrorMessageDto errorMessageDto = new ErrorMessageDto();
        HttpSession session = request.getSession();
        Integer mappedPolicyNo = session.getAttribute(AppConstant.MAPPED_POLICY_NO) == null ? 0 : Integer.parseInt(session.getAttribute(AppConstant.MAPPED_POLICY_NO).toString());
        UserDto sessionUser = getSessionUser(request);
        try {
            ClaimsDto claimsDto = getSessionClaimDetails(request, response);
            claimsDto.setInpUser(sessionUser.getUserId());
            ClaimsDto claim = callCenterService.updatePolicyDetails(claimsDto, mappedPolicyNo, sessionUser);

            if (null != claim) {
                errorMessageDto.setMessage("Mapped Successfully");
            } else {
                errorMessageDto.setMessage("Can not update");
                errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            errorMessageDto.setMessage("Can not update");
            errorMessageDto.setErrorCode(AppConstant.WITH_ERRORS_CODE);
        }
        return errorMessageDto;
    }


}
